[package]
name = "openai-responses"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "Type-safe, async Rust SDK for OpenAI Responses API with advanced features like SIMD JSON parsing, streaming, and zero-copy optimizations"
license = "MIT"
repository = "https://github.com/yourusername/openai-responses-rs"
documentation = "https://docs.rs/openai-responses"
homepage = "https://github.com/yourusername/openai-responses-rs"
readme = "README.md"
keywords = ["openai", "api", "ai", "responses", "sdk"]
categories = ["api-bindings", "web-programming::http-client"]
rust-version = "1.70"

[dependencies]
reqwest = { version = "0.12", features = ["json", "stream", "rustls-tls"], default-features = false }
serde = { version = "1.0", features = ["derive"] }
serde_json = { version = "1.0", features = ["raw_value"] }
tokio = { version = "1.0", features = ["full"] }
thiserror = "1.0"
backon = "0.4"
futures = "0.3"
url = "2.4"
# New dependencies for Rust-specific improvements
anyhow = { version = "1.0", optional = true }
simd-json = { version = "0.13", optional = true }
async-trait = { version = "0.1", optional = true }
tokio-stream = { version = "0.1", optional = true }

[dev-dependencies]
tokio-test = "0.4"
wiremock = "0.6"

[features]
default = ["rustls"]
rustls = ["reqwest/rustls-tls"]
native-tls = ["reqwest/default-tls"]
# Advanced features
enhanced-errors = ["anyhow"]
simd = ["simd-json"]
streaming = ["tokio-stream", "async-trait"]
backend-abstraction = ["async-trait"]

[[example]]
name = "rust_features_demo"
path = "examples/rust_features_demo.rs"

[[example]]
name = "basic_usage"
path = "examples/basic_usage.rs"

[[example]]
name = "async_example"
path = "examples/async_example.rs"

[[example]]
name = "streaming"
path = "examples/streaming.rs"

[[example]]
name = "01_introduction"
path = "examples/01_introduction.rs"

[[example]]
name = "02_text_prompting"
path = "examples/02_text_prompting.rs"

[[example]]
name = "03_conversation_state"
path = "examples/03_conversation_state.rs"

[[example]]
name = "04_function_calling"
path = "examples/04_function_calling.rs"

[[example]]
name = "05_structured_output"
path = "examples/05_structured_output.rs"

[[example]]
name = "06_web_search"
path = "examples/06_web_search.rs"

[[example]]
name = "07_file_search"
path = "examples/07_file_search.rs"

[[example]]
name = "08_reasoning"
path = "examples/08_reasoning.rs"